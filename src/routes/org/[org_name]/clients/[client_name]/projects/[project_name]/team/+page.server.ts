import { fail, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';

export const load = (async ({ params, locals, cookies, depends }) => {
	depends('project:team');
	const { supabase } = locals;

	const { data: projectMembersWithDuplicates, error } = await supabase.rpc(
		'profiles_with_project_access',
		{
			_project_name: params.project_name,
			_client_name: params.client_name,
		},
	);

	if (error) console.error(error);

	type ProjectMember = NonNullable<typeof projectMembersWithDuplicates>[number];

	type ProjectMemberWithRole = ProjectMember & {
		role?: string;
		project_permission_id?: number;
	};

	// Remove duplicates
	const projectMembers =
		projectMembersWithDuplicates?.reduce((acc, curr) => {
			const existingMember = acc.find((member) => member.user_id === curr?.user_id);
			if (!existingMember) {
				acc.push(curr);
			}
			return acc;
		}, [] as ProjectMemberWithRole[]) || [];

	const { project_name, org_name } = params;

	// Fetch project
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select(
			`*, 
			client (name),
			project_permission (
				*,
				profile (*)
			)`,
		)
		.eq('name', project_name)
		.order('created_at', {
			referencedTable: 'project_permission',
			ascending: false,
		})
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		console.error('Error fetching project:', projectError);
		return redirect(
			404,
			`/org/${org_name}/clients/${params.client_name}`,
			{ message: 'Project not found', type: 'error' },
			cookies,
		);
	}

	for (const permission of project.project_permission) {
		const member = projectMembers.find((m) => m.user_id === permission?.user_id);
		if (member) {
			member.role = permission.role;
			member.project_permission_id = permission.project_permission_id;
		}
	}

	// Check if user has permission to manage project
	// TODO: there should be a difference between edit and manage here
	const { data: isProjectAdmin } = await supabase.rpc('can_modify_project', {
		project_id_param: project.project_id,
	});

	// Build names map
	const names = (project.project_permission.map((p) => p.profile) || []).reduce(
		(acc: Record<string, string>, profile) => {
			// Use full_name if available, otherwise use email
			if (profile && profile.user_id)
				acc[profile.user_id] = profile.full_name ?? profile.email ?? 'Unknown user';
			return acc;
		},
		{},
	);

	const { data: invites, error: invitesError } = await supabase
		.from('invite')
		.select('*')
		.eq('resource_id', project.project_id)
		.eq('resource_type', 'project')
		.eq('status', 'pending');

	if (invitesError) {
		console.error('Error fetching invites:', invitesError);
	}

	return {
		projectMembers,
		names,
		invites: invites || [],
		isProjectAdmin,
		project,
		user: locals.user,
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	remove: async ({ request, locals, params }) => {
		const data = await request.formData();
		const permissionId = data.get('permissionId')?.toString();

		if (!permissionId) {
			return fail(400, { error: 'Invalid permission ID' });
		}

		const { supabase } = locals;
		const { project_name, client_name } = params;

		if (!project_name || !client_name) {
			return fail(400, { error: 'Project name and client name are required' });
		}

		// Fetch project id
		const { data: project, error: projError } = await supabase
			.from('project')
			.select('project_id, client_id')
			.eq('name', project_name)
			.limit(1)
			.single();
		if (projError || !project) {
			console.error('Error fetching project:', projError);
			return fail(404, { error: 'Project not found' });
		}

		// Check if user has permission to manage project
		const { data: isProjectAdmin } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!isProjectAdmin) {
			return fail(403, { error: 'You do not have permission to manage team members' });
		}

		// Delete the permission
		const { error: err } = await supabase
			.from('project_permission')
			.delete()
			.eq('project_permission_id', Number(permissionId));

		if (err) {
			console.error('Error removing permission:', err);
			return fail(500, { error: 'Failed to remove user from project' });
		}

		return { success: true };
	},
};
