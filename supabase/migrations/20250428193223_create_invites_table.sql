-- Create enum for resource types
create type public.invite_resource_type as enum('organization', 'client', 'project');

-- Create enum for invite status
create type public.invite_status as enum(
	'pending',
	'accepted',
	'revoked',
	'expired',
	'declined'
);

-- Create invite table
create table public.invite (
	invite_id uuid primary key default gen_random_uuid (),
	resource_type public.invite_resource_type not null,
	resource_id uuid not null,
	role text not null,
	invitee_email text not null,
	token_hash char(64) not null,
	status public.invite_status not null default 'pending',
	inviter_id uuid not null references profile (user_id) on update restrict on delete restrict,
	created_at timestamptz not null default now(),
	updated_at timestamptz not null default now(),
	updated_by uuid references profile (user_id) on update restrict on delete restrict,
	expires_at timestamptz not null,
	unique (invitee_email, resource_type, resource_id)
);

-- Partial index on token_hash for quick lookup
create index invite_token_hash_idx on public.invite (token_hash)
where
	status = 'pending';

-- Enable row level security
alter table public.invite enable row level security;

-- create policy allow reads for project, client, and organization admins
create policy "Admins and invitees can read invites" on public.invite for
select
	to authenticated using (
		-- Check if user has admin access to the entity
		(
			resource_type = 'organization'
			and public.current_user_has_entity_role ('organization', resource_id, 'admin')
		)
		or (
			resource_type = 'client'
			and public.current_user_has_entity_role ('client', resource_id, 'admin')
		) -- Or if the user is the project owner (for project invites)
		or (
			resource_type = 'project'
			and public.current_user_has_entity_role ('project', resource_id, 'owner')
		) -- Or if the user is the invitee
		or exists (
			select
				1
			from
				public.profile p
			where
				p.user_id = (
					select
						auth.uid ()
				)
				and p.email = invitee_email
		)
	);

-- create policy allow updates for project, client, and organization admins
create policy "Admins and the invitee can update invites" on public.invite
for update
	to authenticated using (
		-- Check if user has admin access to the entity
		(
			resource_type = 'organization'
			and public.current_user_has_entity_role ('organization', resource_id, 'admin')
		)
		or (
			resource_type = 'client'
			and public.current_user_has_entity_role ('client', resource_id, 'admin')
		) -- Or if the user is the project owner (for project invites)
		or (
			resource_type = 'project'
			and public.current_user_has_entity_role ('project', resource_id, 'owner')
		) -- Or if the user is the invitee
		or exists (
			select
				1
			from
				public.profile p
			where
				p.user_id = auth.uid ()
				and p.email = invitee_email
		)
	)
with
	check (
		(
			resource_type = 'organization'
			and public.current_user_has_entity_role ('organization', resource_id, 'admin')
		)
		or (
			resource_type = 'client'
			and public.current_user_has_entity_role ('client', resource_id, 'admin')
		)
		or (
			resource_type = 'project'
			and public.current_user_has_entity_role ('project', resource_id, 'owner')
		)
		or exists (
			select
				1
			from
				public.profile p
			where
				p.user_id = (
					select
						auth.uid ()
				)
				and p.email = invitee_email
		)
	);

-- create policy allow deletes for project, client, and organization admins
create policy "Admins can delete invites" on public.invite for delete to authenticated using (
	-- Check if user has admin access to the entity
	(
		resource_type = 'organization'
		and public.current_user_has_entity_role ('organization', resource_id, 'admin')
	)
	or (
		resource_type = 'client'
		and public.current_user_has_entity_role ('client', resource_id, 'admin')
	) -- Or if the user is the project owner (for project invites)
	or (
		resource_type = 'project'
		and public.current_user_has_entity_role ('project', resource_id, 'owner')
	)
);

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.invite to service_role;

-- Create trigger to update the updated_at timestamp on invite
create trigger update_updated_at before
update on public.invite for each row
execute function public.update_updated_at_column ();

-- Create index on invitee_email, status, and expires_at for quick lookup
create index if not exists idx_invite_email_status_expires on public.invite (invitee_email, status, expires_at);

-- Update the accept_invite function to use the new membership table
create or replace function public.accept_invite (token_param char(64)) returns json language plpgsql security definer
set
	search_path = '' as $$
declare v_invite public.invite %rowtype;
v_user_id uuid;
v_entity_type public.entity_type;
v_entity_id uuid;
v_role public.membership_role;
v_resource_type text;
begin -- Get the current user ID
v_user_id := auth.uid();
-- Check if user is authenticated
if v_user_id is null then raise exception 'Not authenticated';
end if;
-- Find the invite by token
select * into v_invite
from public.invite
where token_hash = token_param
	and status = 'pending'
	and expires_at > now();
if not found then raise exception 'Invalid or expired invite token';
end if;
-- Store resource_type as text to avoid casting issues
v_resource_type := v_invite.resource_type::text;
-- Map the resource type to entity_type
if v_resource_type = 'organization' then v_entity_type := 'organization'::public.entity_type;
v_entity_id := v_invite.resource_id;
-- Map organization role
if v_invite.role = 'member' then v_role := 'viewer'::public.membership_role;
else v_role := v_invite.role::public.membership_role;
end if;
elsif v_resource_type = 'client' then v_entity_type := 'client'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
elsif v_resource_type = 'project' then v_entity_type := 'project'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
else raise exception 'Invalid resource type: %',
v_invite.resource_type;
end if;
-- Add the user to the membership table
insert into public.membership(user_id, role, entity_type, entity_id)
values (v_user_id, v_role, v_entity_type, v_entity_id) on conflict (entity_type, entity_id, user_id) do nothing;
-- Mark the invite as accepted
update public.invite
set status = 'accepted'::public.invite_status,
	updated_at = now(),
	updated_by = v_user_id
where invite_id = v_invite.invite_id;
return json_build_object(
	'success',
	true,
	'message',
	'Invite accepted successfully',
	'resource_type',
	v_invite.resource_type,
	'resource_id',
	v_invite.resource_id
);
exception
when others then return json_build_object('success', false, 'message', SQLERRM);
end;
$$;

-- Update the apply_pending_invites function to use the new membership table
create or replace function public.apply_pending_invites () returns trigger language plpgsql security definer
set
	search_path = '' as $$
declare v_invite public.invite %rowtype;
v_entity_type public.entity_type;
v_entity_id uuid;
v_role public.membership_role;
v_resource_type text;
begin -- Loop through all pending invites for this email
begin for v_invite in (
	select *
	from public.invite
	where lower(invitee_email) = lower(new.email)
		and status = 'pending'
		and expires_at > now()
) loop begin -- Store resource_type as text to avoid casting issues
v_resource_type := v_invite.resource_type::text;
-- Map the resource type to entity_type using text comparison
if v_resource_type = 'organization' then v_entity_type := 'organization'::public.entity_type;
v_entity_id := v_invite.resource_id;
-- Map organization role
if v_invite.role = 'member' then v_role := 'viewer'::public.membership_role;
else v_role := v_invite.role::public.membership_role;
end if;
elsif v_resource_type = 'client' then v_entity_type := 'client'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
elsif v_resource_type = 'project' then v_entity_type := 'project'::public.entity_type;
v_entity_id := v_invite.resource_id;
v_role := v_invite.role::public.membership_role;
else continue;
-- Skip invalid resource types
end if;
-- Add the user to the membership table
begin
insert into public.membership(user_id, role, entity_type, entity_id)
values (new.user_id, v_role, v_entity_type, v_entity_id) on conflict (entity_type, entity_id, user_id) do nothing;
-- Mark invite as accepted
update public.invite
set status = 'accepted'::public.invite_status,
	updated_at = now(),
	updated_by = new.user_id
where invite_id = v_invite.invite_id;
exception
when others then -- Log error but continue processing other invites
raise notice 'Error processing invite %: %',
v_invite.invite_id,
SQLERRM;
end;
exception
when others then -- Log error but continue processing other invites
raise notice 'Error mapping invite %: %',
v_invite.invite_id,
SQLERRM;
end;
end loop;
-- Expire any other pending invites that are now out-of-date
begin
update public.invite
set status = 'expired'::public.invite_status,
	updated_at = now(),
	updated_by = new.user_id
where lower(invitee_email) = lower(new.email)
	and status = 'pending'
	and expires_at <= now();
exception
when others then -- Log error but allow user creation to continue
raise notice 'Error expiring outdated invites: %',
SQLERRM;
end;
exception
when others then -- Log error but allow user creation to continue
raise notice 'Error in apply_pending_invites: %',
SQLERRM;
end;
-- Always return new to ensure user creation succeeds
return new;
end;
$$;

-- Create a trigger to apply pending invites when a user registers
-- Using AFTER trigger to ensure the profile is fully created before processing invites
create trigger trg_profile_invites
after insert on public.profile for each row
execute function public.apply_pending_invites ();
