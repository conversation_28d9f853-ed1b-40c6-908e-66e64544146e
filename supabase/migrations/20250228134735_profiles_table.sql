-- Create profile table with necessary columns and defaults
create table public.profile (
	user_id uuid not null primary key,
	email text not null,
	full_name text,
	avatar_url text,
	created_at timestamptz not null default timezone ('utc'::text, now()),
	updated_at timestamptz not null default timezone ('utc'::text, now())
);

comment on table public.profile is 'User profile containing personal information and preferences';

-- Enable Row Level Security
alter table public.profile enable row level security;

-- The service_role is granted full privileges for administrative purposes.
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.profile to service_role;

-- Trigger function: Update the updated_at column before any update
create function public.update_updated_at_column () returns trigger
set
	search_path to public as $$ begin new.updated_at = timezone('utc', now());
return new;
end;
$$ language plpgsql;

comment on function public.update_updated_at_column () is 'Updates the updated_at timestamp when a record is modified';

-- Grant execute privileges on update_updated_at_column function
grant
execute on function public.update_updated_at_column () to service_role;

-- Create trigger on profile to call the update function before any update
create trigger update_updated_at before
update on public.profile for each row
execute function public.update_updated_at_column ();

-- Create policies for profile
create policy "Users can view their own profile" on public.profile for
select
	to authenticated using (
		user_id = (
			select
				auth.uid ()
		)
	);

create policy "Users can update their own profile" on public.profile
for update
	to authenticated using (
		user_id = (
			select
				auth.uid ()
		)
	);

create policy "Users can insert their own profile" on public.profile for insert to authenticated
with
	check (
		user_id = (
			select
				auth.uid ()
		)
	);

-- inserts a row into public.profiles
create function public.handle_new_user () returns trigger language plpgsql security definer
set
	search_path = '' as $$ begin
insert into public.profile (user_id, email, full_name)
values (
		new.id,
		new.email,
		new.raw_user_meta_data->>'full_name'
	);
return new;
end;
$$;

-- trigger the function every time a user is created
create trigger on_auth_user_created
after insert on auth.users for each row
execute procedure public.handle_new_user ();
