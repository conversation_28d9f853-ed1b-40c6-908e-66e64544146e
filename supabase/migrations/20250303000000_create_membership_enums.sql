-- Create entity_type enum for polymorphic membership
create type "public"."entity_type" as enum('organization', 'client', 'project');

-- Create membership_role enum that combines all existing role types
create type "public"."membership_role" as enum('viewer', 'editor', 'admin', 'owner');

-- Create membership table to replace organization_member, client_permission, and project_permission
create table "public"."membership" (
	membership_id bigint generated always as identity primary key,
	user_id uuid not null references profile (user_id) on update restrict on delete restrict,
	role membership_role not null,
	entity_type entity_type not null,
	entity_id uuid not null,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."membership" is 'Polymorphic membership table for organization, client, and project access control';

-- Create a unique index to prevent duplicate roles for the same user and entity
create unique index membership_unique_entity_user on public.membership (entity_type, entity_id, user_id);

-- Create trigger to update the updated_at timestamp
create trigger update_updated_at before
update on public.membership for each row
execute function public.update_updated_at_column ();

-- Get all ancestor entities for a given entity
create or replace function public.get_entity_ancestors (
	entity_type_param entity_type,
	entity_id_param uuid
) returns table (entity_type entity_type, entity_id uuid) language plpgsql security definer as $$ begin -- Return the entity itself
	return query
select entity_type_param,
	entity_id_param;
-- If it's a project, add its client
if entity_type_param = 'project' then return query
select 'client'::entity_type,
	client_id
from project
where project_id = entity_id_param;
-- Also add the organization of the client
return query
select 'organization'::entity_type,
	c.org_id
from project p
	join client c on p.client_id = c.client_id
where p.project_id = entity_id_param;
end if;
-- If it's a client, add its organization
if entity_type_param = 'client' then return query
select 'organization'::entity_type,
	org_id
from client
where client_id = entity_id_param;
end if;
return;
end;
$$;

comment on function public.get_entity_ancestors (entity_type, uuid) is 'Returns all ancestor entities for a given entity';

-- Check if a user has access to an entity
create or replace function public.has_entity_access (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) returns boolean language plpgsql security definer as $$ begin return exists (
		select 1
		from membership m
			join lateral get_entity_ancestors(entity_type_param, entity_id_param) a on true
		where m.user_id = user_id_param
			and m.entity_type = a.entity_type
			and m.entity_id = a.entity_id
	);
end;
$$;

comment on function public.has_entity_access (uuid, entity_type, uuid) is 'Checks if a user has access to an entity through direct membership or ancestor entities';

-- Check if current user has access to an entity
create or replace function public.current_user_has_entity_access (
	entity_type_param entity_type,
	entity_id_param uuid
) returns boolean language plpgsql security definer as $$ begin return public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
end;
$$;

comment on function public.current_user_has_entity_access (entity_type, uuid) is 'Checks if the current user has access to an entity';

-- Get effective role for a user on an entity
create or replace function public.get_effective_role (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid
) returns membership_role language plpgsql security definer as $$
declare effective_role membership_role;
begin -- Check for direct membership first
select role into effective_role
from membership
where user_id = user_id_param
	and entity_type = entity_type_param
	and entity_id = entity_id_param;
if found then return effective_role;
end if;
-- Check for client-level role if this is a project
if entity_type_param = 'project' then
select m.role into effective_role
from membership m
	join project p on p.project_id = entity_id_param
where m.user_id = user_id_param
	and m.entity_type = 'client'
	and m.entity_id = p.client_id;
if found then return effective_role;
end if;
end if;
-- Check for organization-level role
if entity_type_param = 'project' then
select m.role into effective_role
from membership m
	join project p on p.project_id = entity_id_param
	join client c on p.client_id = c.client_id
where m.user_id = user_id_param
	and m.entity_type = 'organization'
	and m.entity_id = c.org_id;
elsif entity_type_param = 'client' then
select m.role into effective_role
from membership m
	join client c on c.client_id = entity_id_param
where m.user_id = user_id_param
	and m.entity_type = 'organization'
	and m.entity_id = c.org_id;
end if;
return effective_role;
end;
$$;

comment on function public.get_effective_role (uuid, entity_type, uuid) is 'Gets the effective role for a user on an entity, considering the hierarchy';

-- Check if a user has a specific role or higher on an entity
create or replace function public.has_entity_role (
	user_id_param uuid,
	entity_type_param entity_type,
	entity_id_param uuid,
	min_role_param membership_role
) returns boolean language plpgsql security definer as $$
declare user_role membership_role;
begin user_role := get_effective_role(
	user_id_param,
	entity_type_param,
	entity_id_param
);
if user_role is null then return false;
end if;
-- Role hierarchy: viewer < editor < admin < owner
if min_role_param = 'viewer' then return true;
-- Any role is sufficient
elsif min_role_param = 'editor' then return user_role in ('editor', 'admin', 'owner');
elsif min_role_param = 'admin' then return user_role in ('admin', 'owner');
elsif min_role_param = 'owner' then return user_role = 'owner';
end if;
return false;
end;
$$;

comment on function public.has_entity_role (uuid, entity_type, uuid, membership_role) is 'Checks if a user has a specific role or higher on an entity';

-- Check if current user has a specific role or higher on an entity
create or replace function public.current_user_has_entity_role (
	entity_type_param entity_type,
	entity_id_param uuid,
	min_role_param membership_role
) returns boolean language plpgsql security definer as $$ begin return public.has_entity_role(
		auth.uid(),
		entity_type_param,
		entity_id_param,
		min_role_param
	);
end;
$$;

comment on function public.current_user_has_entity_role (entity_type, uuid, membership_role) is 'Checks if the current user has a specific role or higher on an entity';

-- Enable RLS on membership table
alter table membership enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.membership to service_role;
