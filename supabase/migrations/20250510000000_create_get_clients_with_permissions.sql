CREATE OR REPLACE FUNCTION get_clients_with_permissions (org_name_param TEXT) RETURNS TABLE (
	client_id UUID,
	name TEXT,
	description TEXT,
	logo_url TEXT,
	client_url TEXT,
	internal_url TEXT,
	internal_url_description TEXT,
	org_id UUID,
	created_at TIMESTAMPTZ,
	updated_at TIMESTAMPTZ,
	created_by_user_id UUID,
	organization_name TEXT,
	project_count BIGINT,
	is_client_admin BOOLEAN,
	is_org_admin BOOLEAN
) LANGUAGE SQL SECURITY DEFINER AS $$
SELECT c.client_id,
	c.name,
	c.description,
	c.logo_url,
	c.client_url,
	c.internal_url,
	c.internal_url_description,
	c.org_id,
	c.created_at,
	c.updated_at,
	c.created_by_user_id,
	o.name AS organization_name,
	(
		SELECT COUNT(*)
		FROM project p
		WHERE p.client_id = c.client_id
	) AS project_count,
	current_user_has_entity_role('client', c.client_id, 'admin') AS is_client_admin,
	current_user_has_entity_role('organization', c.org_id, 'admin') AS is_org_admin
FROM client c
	INNER JOIN organization o ON o.org_id = c.org_id
WHERE o.name = org_name_param
ORDER BY c.name;
$$;

-- Grant execute to authenticated users
GRANT
EXECUTE ON FUNCTION get_clients_with_permissions (TEXT) TO authenticated;

GRANT
EXECUTE ON FUNCTION get_clients_with_permissions (TEXT) TO service_role;

COMMENT ON FUNCTION get_clients_with_permissions (TEXT) IS 'Get all clients for an organization with project counts and permission info';
