-- Create item_type enum
CREATE TYPE wbs_item_type AS ENUM('Standard', 'Custom');

-- WBS Library Item Table - Hierarchical project structures
create table "public"."wbs_library_item" (
	wbs_library_item_id bigint generated always as identity primary key,
	wbs_library_id bigint not null references wbs_library (wbs_library_id) on update restrict on delete restrict,
	level integer not null,
	in_level_code text not null,
	parent_item_id bigint references wbs_library_item (wbs_library_item_id),
	code text not null,
	description text not null,
	cost_scope text,
	item_type wbs_item_type not null default 'Custom',
	client_id uuid references client (client_id),
	project_id uuid references project (project_id),
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null
);

comment on table "public"."wbs_library_item" is 'Individual items within WBS library forming hierarchical project structures';

comment on column "public"."wbs_library_item"."cost_scope" is 'Detailed cost scope information for this WBS item';

comment on column "public"."wbs_library_item"."item_type" is 'Type of WBS item - Standard (global library) or Custom (client/project specific)';

comment on column "public"."wbs_library_item"."client_id" is 'The client this custom WBS item belongs to (null for standard items)';

comment on column "public"."wbs_library_item"."project_id" is 'The project this custom WBS item belongs to (null means client-wide or standard)';

-- Create trigger on wbs_library_item to call the update function before any update
create trigger update_updated_at before
update on public.wbs_library_item for each row
execute function public.update_updated_at_column ();

alter table wbs_library_item enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.wbs_library_item to service_role;

-- WBS library item policies
create policy "Users can view standard WBS library item" on wbs_library_item for
select
	to authenticated using (item_type = 'Standard');

create policy "Users can view custom WBS library item they have access to" on wbs_library_item for
select
	to authenticated using (
		item_type = 'Custom'
		AND (
			-- User can see the item if they have access to the client
			EXISTS (
				SELECT
					1
				FROM
					client c
					LEFT JOIN client_permission cp ON c.client_id = cp.client_id
				WHERE
					c.client_id = wbs_library_item.client_id
					AND cp.user_id = auth.uid ()
			)
			OR (
				-- Or if they have access to the project
				project_id IS NOT NULL
				AND can_access_project (project_id)
			)
		)
	);

-- Service role can manage standard library items
create policy "Service role can insert standard WBS library item" on wbs_library_item for insert to service_role
with
	check (item_type = 'Standard');

create policy "Service role can update standard WBS library item" on wbs_library_item
for update
	to service_role using (item_type = 'Standard');

create policy "Service role can delete standard WBS library item" on wbs_library_item for delete to service_role using (item_type = 'Standard');

-- Check function for client admin permission
create or replace function can_modify_client_wbs (client_id_param uuid) returns boolean as $$ begin return exists (
		select 1
		from client_permission
		where client_id = client_id_param
			and user_id = auth.uid()
			and role = 'admin'
	);
end;
$$ language plpgsql security definer;

-- Client/project admins can manage custom items
create policy "Client/project admins can insert custom WBS library item" on wbs_library_item for insert to authenticated
with
	check (
		item_type = 'Custom'
		AND (
			(
				project_id IS NOT NULL
				AND can_modify_project (project_id)
			)
			OR (can_modify_client_wbs (client_id))
		)
	);

create policy "Client/project admins can update custom WBS library item" on wbs_library_item
for update
	to authenticated using (
		item_type = 'Custom'
		AND (
			(
				project_id IS NOT NULL
				AND can_modify_project (project_id)
			)
			OR (can_modify_client_wbs (client_id))
		)
	)
with
	check (
		item_type = 'Custom'
		and (
			(
				project_id is not null
				and can_modify_project (project_id)
			)
			or can_modify_client_wbs (client_id)
		)
	);

create policy "Client/project admins can delete custom WBS library item" on wbs_library_item for delete to authenticated using (
	item_type = 'Custom'
	AND (
		(
			project_id IS NOT NULL
			AND can_modify_project (project_id)
		)
		OR (can_modify_client_wbs (client_id))
	)
);
