create or replace function profiles_with_project_access (_project_name text, _client_name text) returns table (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamptz,
	updated_at timestamptz,
	access_via text,
	role text
) language sql stable security definer
set search_path = '' as $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'organization'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization'
	and m.entity_id = c.org_id
	join public.project pr on pr.client_id = c.client_id
where c.name = _client_name
	and pr.name = _project_name
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'client'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client'
	and m.entity_id = c.client_id
	join public.project pr on pr.client_id = c.client_id
where c.name = _client_name
	and pr.name = _project_name
union all
/* ── via PROJECT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	'project'::text as access_via,
	m.role::text as role
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.project pr on m.entity_type = 'project'
	and m.entity_id = pr.project_id
	join public.client c on c.client_id = pr.client_id
where c.name = _client_name
	and pr.name = _project_name
order by created_at desc;
$$;

grant
execute on function profiles_with_project_access (text, text) to authenticated;

grant
execute on function profiles_with_project_access (text, text) to service_role;
